package org.technoserve.udp.entity.common;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity representing an Irrigation Method master data
 */
@Entity
@Table(name = "irrigation_method")
@Getter
@Setter
@NoArgsConstructor
public class IrrigationMethod {

    @Id
    @Column(name = "method_name", length = 100)
    private String methodName;

    @Column(name = "description", length = 255)
    private String description;

}
