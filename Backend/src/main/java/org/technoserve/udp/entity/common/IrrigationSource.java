package org.technoserve.udp.entity.common;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Entity representing an Irrigation Source master data
 */
@Entity
@Table(name = "irrigation_source")
@Getter
@Setter
@NoArgsConstructor
public class IrrigationSource {

    @Id
    @Column(name = "source_name", length = 100)
    private String sourceName;

    @Column(name = "description", length = 255)
    private String description;

}
