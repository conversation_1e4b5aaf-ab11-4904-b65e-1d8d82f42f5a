package org.technoserve.udp.service;

import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.technoserve.udp.dto.CottonFarmingReportDto;
import org.technoserve.udp.dto.CottonFarmingYearDataDto;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Service for exporting data to Excel format
 */
@Service
@RequiredArgsConstructor
public class ExcelExportService {

    /**
     * Generate Excel file for Cotton Farming Report
     *
     * @param reportData List of cotton farming report DTOs
     * @return byte array of the Excel file
     * @throws IOException if there's an error creating the Excel file
     */
    public byte[] generateCottonFarmingReportExcel(List<CottonFarmingReportDto> reportData) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            CellStyle numberStyle = createNumberStyle(workbook);

            // Create Farmer Summary Sheet
            Sheet farmerSheet = workbook.createSheet("Farmer Summary");
            createFarmerSummarySheet(farmerSheet, reportData, headerStyle, dataStyle, numberStyle);

            // Create Cotton Farming Details Sheet
            Sheet detailsSheet = workbook.createSheet("Cotton Farming Details");
            createCottonFarmingDetailsSheet(detailsSheet, reportData, headerStyle, dataStyle, numberStyle);

            // Write to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * Create farmer summary sheet with basic farmer information
     */
    private void createFarmerSummarySheet(Sheet sheet, List<CottonFarmingReportDto> reportData, 
                                        CellStyle headerStyle, CellStyle dataStyle, CellStyle numberStyle) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] farmerHeaders = {
            "Farmer ID", "Farmer Tracenet Code", "Farmer Name", "Age", "Gender", "State", "District", 
            "Village", "Mobile Number", "Centre ID", "Centre Name", "Centre Type", "Marital Status", 
            "Spouse Name", "Caste", "Highest Education", "Household Size", "Land Size Under Cultivation", 
            "Land Measure Type", "Organic Status", "Herd Size", "Other Income Generating Activity", 
            "Household Annual Income", "Agricultural Annual Income", "Dairy Annual Income", 
            "Other Annual Income", "Crops Grown", "Cattle Breed Types", "Loan Amount", 
            "Agricultural Loan", "Dairy Loan", "Lat Long", "Partner Name", "Program Name"
        };

        for (int i = 0; i < farmerHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(farmerHeaders[i]);
            cell.setCellStyle(headerStyle);
        }

        // Populate data rows
        int rowNum = 1;
        for (CottonFarmingReportDto farmer : reportData) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;

            setCellValue(row.createCell(colNum++), farmer.getFarmerId(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getFarmerTracenetCode(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getFarmerName(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getAge(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getGender(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getState(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getDistrict(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getVillage(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getMobileNumber(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getCentreId(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getCentreName(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getCentreType(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getMaritalStatus(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getSpouseName(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getCaste(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getHighestEducation(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getHouseHoldSize(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getLandSizeUnderCultivation(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getLandMeasureType(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getOrganicStatus(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getHerdSize(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getAnyOtherIncomeGeneratingActivity(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getHouseholdAnnualIncome(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getAgriculturalAnnualIncome(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getDairyAnnualIncome(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getOtherAnnualIncome(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getCropsGrown(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getCattleBreedTypes(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getLoanAmount(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getAgriculturalLoan(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getDairyLoan(), numberStyle);
            setCellValue(row.createCell(colNum++), farmer.getLatLong(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getPartnerName(), dataStyle);
            setCellValue(row.createCell(colNum++), farmer.getProgramName(), dataStyle);
        }

        // Auto-size columns
        for (int i = 0; i < farmerHeaders.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create cotton farming details sheet with year-wise data
     */
    private void createCottonFarmingDetailsSheet(Sheet sheet, List<CottonFarmingReportDto> reportData, 
                                               CellStyle headerStyle, CellStyle dataStyle, CellStyle numberStyle) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] detailHeaders = {
            "Farmer ID", "Farmer Name", "Partner Name", "Program Name", "Year", 
            "Males in Household", "Females in Household", "Children in Household", "School Going Children", 
            "Earning Members", "Total Landholding", "Primary Crop", "Secondary Crops", 
            "Non-Organic Cotton Land", "Organic Cotton Land", "Years Organic Practice", "Certification Status", 
            "Irrigation Source", "Cattle Count", "Drinking Water Source", "Preferred Selling Point", 
            "Has Storage Space", "Receives Agro Advisory", "Received Training", "Membership in Org", 
            "Maintains Records", "Annual Household Income", "Primary Income Source", "Primary Income Amount", 
            "Certification Cost Per Acre", "Avg Production Per Acre", "Cost of Cultivation Per Acre", 
            "Organic Cotton Quantity Sold", "Selling Price Per Kg", "Bio Inputs Cost", 
            "Pest Management Bio Inputs", "Bio Fertilizer Used", "Pheromone Traps Per Acre", 
            "Yellow Sticky Traps Per Acre", "Blue Sticky Traps Per Acre", "Bird Perches Per Acre", 
            "Irrigation Cost Per Acre", "Irrigation Count", "Irrigation Method", "Farm Machinery Hired", 
            "Machinery Hiring Cost", "Local Labour Cost Per Day", "Migrant Labour Cost Per Day", 
            "Workers for Sowing", "Workers for Harvesting", "Harvesting Time", "Weeding Method", 
            "Weeding Cost Per Acre", "Mulching Cost Per Acre", "Tillage Count", "Tillage Cost Per Acre", 
            "Land Preparation Cost", "Organic Cotton Seed Rate", "Organic Cotton Seed Variety", 
            "Border Crop", "Inter Crop", "Cover Crop", "Trap Crop", "Mulching Used", "Mulching Type", 
            "Storage Precautions", "Hired Vehicle for Transport", "Transportation Cost Per Kg", 
            "Rejected Quantity", "Price Discovery Mechanism", "Payment Transaction Type", "Credit Days", 
            "Govt Scheme Availed", "Crop Insurance", "Crop Insurance Cost Per Acre", "Has KCC", 
            "Has Active Bank Account", "Crop Rotation Used", "Rotation Crops", "Water Tracking Devices", 
            "Pump Capacity", "Buffer Zone", "Crop Residue Utilization", "Worker Payment Mode", 
            "Wage Gender Difference", "Labour Register", "Safety Kit for Workers", 
            "Shelter and Water for Workers", "Lavatory for Workers", "Women in Agri Operations", 
            "Community Water Harvesting", "Soil Moisture Meter Used", "Total HH Members", 
            "Dependency Ratio", "Gender Ratio", "School Attendance Rate", "Total Cotton Land", 
            "Organic Percent", "Land Used for Cotton", "Income Per Earner", "OC Income", 
            "Profit Per Acre", "Total Certification Cost", "Total PT Cost", "Total YST Cost", 
            "Total BST Cost", "Total Pest Mgmt Cost", "Total Labour Cost", "Machinery Cost Total", 
            "Total Irrigation Cost", "Irrigation Frequency"
        };

        for (int i = 0; i < detailHeaders.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(detailHeaders[i]);
            cell.setCellStyle(headerStyle);
        }

        // Populate data rows
        int rowNum = 1;
        for (CottonFarmingReportDto farmer : reportData) {
            for (CottonFarmingYearDataDto yearData : farmer.getYearData()) {
                Row row = sheet.createRow(rowNum++);
                int colNum = 0;

                // Farmer basic info
                setCellValue(row.createCell(colNum++), farmer.getFarmerId(), dataStyle);
                setCellValue(row.createCell(colNum++), farmer.getFarmerName(), dataStyle);
                setCellValue(row.createCell(colNum++), farmer.getPartnerName(), dataStyle);
                setCellValue(row.createCell(colNum++), farmer.getProgramName(), dataStyle);

                // Year data
                setCellValue(row.createCell(colNum++), yearData.getYear(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getMalesInHousehold(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getFemalesInHousehold(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getChildrenInHousehold(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getSchoolGoingChildren(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getEarningMembers(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalLandholding(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getPrimaryCrop(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getSecondaryCrops(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getNonOrganicCottonLand(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getOrganicCottonLand(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getYearsOrganicPractice(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getCertificationStatus(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getIrrigationSource(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCattleCount(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getDrinkingWaterSource(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getPreferredSellingPoint(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getHasStorageSpace(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getReceivesAgroAdvisory(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getReceivedTraining(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getMembershipInOrg(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getMaintainsRecords(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getAnnualHouseholdIncome(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getPrimaryIncomeSource(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getPrimaryIncomeAmount(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getCertificationCostPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getAvgProductionPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getCostOfCultivationPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getOrganicCottonQuantitySold(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getSellingPricePerKg(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getBioInputsCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getPestManagementBioInputs(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getBioFertilizerUsed(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getPheromoneTrapsPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getYellowStickyTrapsPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getBlueStickyTrapsPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getBirdPerchesPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getIrrigationCostPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getIrrigationCount(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getIrrigationMethod(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getFarmMachineryHired(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getMachineryHiringCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getLocalLabourCostPerDay(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getMigrantLabourCostPerDay(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getWorkersForSowing(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getWorkersForHarvesting(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getHarvestingTime(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getWeedingMethod(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getWeedingCostPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getMulchingCostPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTillageCount(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTillageCostPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getLandPreparationCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getOrganicCottonSeedRate(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getOrganicCottonSeedVariety(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getBorderCrop(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getInterCrop(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCoverCrop(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getTrapCrop(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getMulchingUsed(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getMulchingType(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getStoragePrecautions(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getHiredVehicleForTransport(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getTransportationCostPerKg(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getRejectedQuantity(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getPriceDiscoveryMechanism(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getPaymentTransactionType(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCreditDays(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getGovtSchemeAvailed(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCropInsurance(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCropInsuranceCostPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getHasKCC(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getHasActiveBankAccount(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCropRotationUsed(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getRotationCrops(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getWaterTrackingDevices(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getPumpCapacity(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getBufferZone(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCropResidueUtilization(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getWorkerPaymentMode(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getWageGenderDifference(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getLabourRegister(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getSafetyKitForWorkers(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getShelterAndWaterForWorkers(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getLavatoryForWorkers(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getWomenInAgriOperations(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getCommunityWaterHarvesting(), dataStyle);
                setCellValue(row.createCell(colNum++), yearData.getSoilMoistureMeterUsed(), dataStyle);
                
                // Calculated fields
                setCellValue(row.createCell(colNum++), yearData.getTotalHHMembers(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getDependencyRatio(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getGenderRatio(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getSchoolAttendanceRate(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalCottonLand(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getOrganicPercent(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getLandUsedForCotton(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getIncomePerEarner(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getOcIncome(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getProfitPerAcre(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalCertificationCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalPTCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalYSTCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalBSTCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalPestMgmtCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalLabourCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getMachineryCostTotal(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getTotalIrrigationCost(), numberStyle);
                setCellValue(row.createCell(colNum++), yearData.getIrrigationFrequency(), numberStyle);
            }
        }

        // Auto-size columns (limit to first 20 columns for performance)
        for (int i = 0; i < Math.min(detailHeaders.length, 20); i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * Create header cell style
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * Create data cell style
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * Create number cell style
     */
    private CellStyle createNumberStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        DataFormat dataFormat = workbook.createDataFormat();
        style.setDataFormat(dataFormat.getFormat("#,##0.00"));
        return style;
    }

    /**
     * Set cell value with appropriate type handling
     */
    private void setCellValue(Cell cell, Object value, CellStyle style) {
        cell.setCellStyle(style);
        
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * Generate filename for the Excel export
     */
    public String generateFileName(String reportType, Long programId, Long partnerId, List<Integer> years) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
        String timestamp = LocalDateTime.now().format(formatter);
        
        StringBuilder filename = new StringBuilder(reportType);
        filename.append("_Report");
        
        if (programId != null) {
            filename.append("_Program").append(programId);
        }
        
        if (partnerId != null) {
            filename.append("_Partner").append(partnerId);
        }
        
        if (years != null && !years.isEmpty()) {
            filename.append("_Years");
            years.forEach(year -> filename.append("_").append(year));
        }
        
        filename.append("_").append(timestamp).append(".xlsx");
        
        return filename.toString();
    }
}
