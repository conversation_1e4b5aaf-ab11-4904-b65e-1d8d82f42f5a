package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.IrrigationSource;

import java.util.List;

@Repository
public interface IrrigationSourceRepository extends JpaRepository<IrrigationSource, String> {
    
    /**
     * Find all irrigation sources ordered by source name
     * 
     * @return List of irrigation sources ordered by source name
     */
    List<IrrigationSource> findAllByOrderBySourceNameAsc();
}
