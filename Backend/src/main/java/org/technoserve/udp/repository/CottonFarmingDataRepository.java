package org.technoserve.udp.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.dataflow.CottonFarmingData;
import org.technoserve.udp.entity.dataflow.CottonFarmingDataId;

import java.util.List;
import java.util.Optional;

@Repository
public interface CottonFarmingDataRepository extends JpaRepository<CottonFarmingData, CottonFarmingDataId> {

    Optional<CottonFarmingData> findByFarmerIdAndProgramIdAndPartnerIdAndYear(String farmerId, Long programId, Long partnerId, Integer year);

    void deleteAllByExcelFileMetaData_ExcelFileMetaDataId(Long excelFileMetaDataId);

    /**
     * Find farmers with their cotton farming data by program ID, partner ID and years
     *
     * @param programId The program ID (optional)
     * @param partnerId The partner ID (optional)
     * @param years The list of years to filter by (optional)
     * @return List of Object arrays containing farmer and cotton farming data
     */
    @Query("SELECT f, c FROM Farmer f " +
        "JOIN CottonFarmingData c ON f.farmerId = c.farmerId " +
        "AND f.programId = c.programId AND f.partnerId = c.partnerId " +
        "WHERE (:programId IS NULL OR f.programId = :programId) " +
        "AND (:partnerId IS NULL OR f.partnerId = :partnerId) " +
        "AND (c.year IN :years) " +
        "ORDER BY f.farmerId, c.year")
    List<Object[]> findFarmersWithCottonData(
        @Param("programId") Long programId,
        @Param("partnerId") Long partnerId,
        @Param("years") List<Integer> years);

}
