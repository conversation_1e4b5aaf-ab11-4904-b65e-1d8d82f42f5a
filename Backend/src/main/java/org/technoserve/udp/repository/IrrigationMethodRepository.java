package org.technoserve.udp.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.technoserve.udp.entity.common.IrrigationMethod;

import java.util.List;

@Repository
public interface IrrigationMethodRepository extends JpaRepository<IrrigationMethod, String> {
    
    /**
     * Find all irrigation methods ordered by method name
     * 
     * @return List of irrigation methods ordered by method name
     */
    List<IrrigationMethod> findAllByOrderByMethodNameAsc();
}
