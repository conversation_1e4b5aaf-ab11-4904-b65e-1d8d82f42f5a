package org.technoserve.udp.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.List;

/**
 * DTO for Cotton Farming Report with farmer properties and yearly cotton farming data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CottonFarmingReportDto {

    // Farmer fields
    private String farmerId;
    private String farmerTracenetCode;
    private String farmerName;
    private Integer age;
    private String gender;
    private String state;
    private String district;
    private String village;
    private String mobileNumber;
    private String centreId;
    private String centreName;
    private String centreType;
    private String maritalStatus;
    private String spouseName;
    private String caste;
    private String highestEducation;
    private Integer houseHoldSize;
    private Double landSizeUnderCultivation;
    private String landMeasureType;
    private String organicStatus;
    private Integer herdSize;
    private String anyOtherIncomeGeneratingActivity;
    private BigDecimal householdAnnualIncome;
    private BigDecimal agriculturalAnnualIncome;
    private BigDecimal dairyAnnualIncome;
    private BigDecimal otherAnnualIncome;
    private String cropsGrown;
    private String cattleBreedTypes;
    private BigDecimal loanAmount;
    private BigDecimal agriculturalLoan;
    private BigDecimal dairyLoan;
    private String latLong;

    // Program and Partner info
    private String partnerName;
    private String programName;

    // Array of cotton farming data for different years
    private List<CottonFarmingYearDataDto> yearData;
}
