-- Create irrigation_source table
CREATE TABLE IF NOT EXISTS public.irrigation_source (
    source_name character varying(100) NOT NULL,
    description character varying(255),
    CONSTRAINT irrigation_source_pkey PRIMARY KEY (source_name)
);

-- Create irrigation_method table
CREATE TABLE IF NOT EXISTS public.irrigation_method (
    method_name character varying(100) NOT NULL,
    description character varying(255),
    CONSTRAINT irrigation_method_pkey PRIMARY KEY (method_name)
);

-- Insert sample irrigation sources
INSERT INTO public.irrigation_source (source_name, description) VALUES
('Borewell', 'Water extracted from underground through borewell'),
('Canal', 'Water from irrigation canals'),
('River', 'Water from nearby rivers'),
('Pond', 'Water from farm ponds or tanks'),
('Rainwater Harvesting', 'Collected rainwater for irrigation'),
('Tube Well', 'Water from tube wells'),
('Open Well', 'Water from open wells'),
('Tank', 'Water from storage tanks'),
('Stream', 'Water from natural streams'),
('Lake', 'Water from lakes or reservoirs')
ON CONFLICT (source_name) DO NOTHING;

-- Insert sample irrigation methods
INSERT INTO public.irrigation_method (method_name, description) VALUES
('Drip Irrigation', 'Water applied directly to plant roots through drippers'),
('Sprinkler Irrigation', 'Water sprayed over crops through sprinkler systems'),
('Flood Irrigation', 'Traditional method where water floods the entire field'),
('Furrow Irrigation', 'Water flows through furrows between crop rows'),
('Basin Irrigation', 'Water applied to level basins around plants'),
('Border Irrigation', 'Water flows down gentle slopes in strips'),
('Micro Sprinkler', 'Small sprinklers for localized irrigation'),
('Surface Irrigation', 'Water applied and distributed over the soil surface'),
('Subsurface Irrigation', 'Water applied below the soil surface'),
('Manual Watering', 'Hand watering using buckets or hoses')
ON CONFLICT (method_name) DO NOTHING;

-- Set table ownership
ALTER TABLE public.irrigation_source OWNER TO postgres;
ALTER TABLE public.irrigation_method OWNER TO postgres;
