package org.technoserve.udp.controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.technoserve.udp.dto.CottonFarmingReportDto;
import org.technoserve.udp.service.ExcelExportService;
import org.technoserve.udp.service.MasterReportService;
import org.technoserve.udp.service.TransactionalReportService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReportControllerTest {

    @Mock
    private TransactionalReportService reportService;

    @Mock
    private MasterReportService masterReportService;

    @Mock
    private ExcelExportService excelExportService;

    @InjectMocks
    private ReportController reportController;

    @Test
    void testDownloadCottonFarmingReport_Success() throws Exception {
        // Given
        List<Integer> years = Arrays.asList(2023, 2024);
        Long programId = 1L;
        Long partnerId = 1L;
        
        // Mock report data
        CottonFarmingReportDto mockReportDto = CottonFarmingReportDto.builder()
                .farmerId("F001")
                .farmerName("Test Farmer")
                .build();
        
        List<CottonFarmingReportDto> mockReportData = Arrays.asList(mockReportDto);
        
        Map<String, Object> mockResponse = new HashMap<>();
        mockResponse.put("data", mockReportData);
        
        // Mock Excel data
        byte[] mockExcelData = "mock excel data".getBytes();
        String mockFilename = "Cotton_Farming_Report_Program1_Partner1_Years_2023_2024_20240527_120000.xlsx";
        
        // When
        when(reportService.generateCottonFarmingReport(
                eq(partnerId), eq(programId), eq(years), eq("farmerName"), eq("asc"), eq(0), eq(Integer.MAX_VALUE)))
                .thenReturn(mockResponse);
        
        when(excelExportService.generateCottonFarmingReportExcel(mockReportData))
                .thenReturn(mockExcelData);
        
        when(excelExportService.generateFileName("Cotton_Farming", programId, partnerId, years))
                .thenReturn(mockFilename);
        
        // Then
        ResponseEntity<byte[]> response = reportController.downloadCottonFarmingReport(
                partnerId, programId, years, "farmerName", "asc");
        
        // Verify
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(mockExcelData, response.getBody());
        
        // Verify headers
        assertNotNull(response.getHeaders().getContentType());
        assertNotNull(response.getHeaders().getContentDisposition());
        assertEquals(mockExcelData.length, response.getHeaders().getContentLength());
    }

    @Test
    void testDownloadCottonFarmingReport_Exception() {
        // Given
        List<Integer> years = Arrays.asList(2023);
        
        // When - simulate exception
        when(reportService.generateCottonFarmingReport(
                any(), any(), any(), any(), any(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Test exception"));
        
        // Then
        ResponseEntity<byte[]> response = reportController.downloadCottonFarmingReport(
                null, null, years, "farmerName", "asc");
        
        // Verify
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNull(response.getBody());
    }
}
