package org.technoserve.udp.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.technoserve.udp.dto.CottonFarmingReportDto;
import org.technoserve.udp.dto.CottonFarmingYearDataDto;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ExcelExportServiceTest {

    @InjectMocks
    private ExcelExportService excelExportService;

    private List<CottonFarmingReportDto> testData;

    @BeforeEach
    void setUp() {
        // Create test data
        CottonFarmingYearDataDto yearData1 = CottonFarmingYearDataDto.builder()
                .year(2023)
                .malesInHousehold(2)
                .femalesInHousehold(2)
                .childrenInHousehold(1)
                .schoolGoingChildren(1)
                .earningMembers(2)
                .totalLandholding(5.0)
                .primaryCrop("Cotton")
                .organicCottonLand(3.0)
                .nonOrganicCottonLand(2.0)
                .certificationStatus("Certified")
                .irrigationSource("Borewell")
                .annualHouseholdIncome(new BigDecimal("150000"))
                .avgProductionPerAcre(500.0)
                .sellingPricePerKg(new BigDecimal("45.50"))
                .build();

        CottonFarmingYearDataDto yearData2 = CottonFarmingYearDataDto.builder()
                .year(2024)
                .malesInHousehold(2)
                .femalesInHousehold(2)
                .childrenInHousehold(1)
                .schoolGoingChildren(1)
                .earningMembers(2)
                .totalLandholding(5.0)
                .primaryCrop("Cotton")
                .organicCottonLand(4.0)
                .nonOrganicCottonLand(1.0)
                .certificationStatus("Certified")
                .irrigationSource("Borewell")
                .annualHouseholdIncome(new BigDecimal("180000"))
                .avgProductionPerAcre(550.0)
                .sellingPricePerKg(new BigDecimal("48.00"))
                .build();

        CottonFarmingReportDto farmer1 = CottonFarmingReportDto.builder()
                .farmerId("F001")
                .farmerName("John Doe")
                .age(35)
                .gender("Male")
                .state("Maharashtra")
                .district("Nagpur")
                .village("Test Village")
                .mobileNumber("9876543210")
                .centreId("C001")
                .centreName("Test Centre")
                .centreType("Primary")
                .partnerName("Test Partner")
                .programName("Test Program")
                .landSizeUnderCultivation(5.0)
                .organicStatus("Certified")
                .householdAnnualIncome(new BigDecimal("200000"))
                .yearData(Arrays.asList(yearData1, yearData2))
                .build();

        CottonFarmingReportDto farmer2 = CottonFarmingReportDto.builder()
                .farmerId("F002")
                .farmerName("Jane Smith")
                .age(32)
                .gender("Female")
                .state("Gujarat")
                .district("Ahmedabad")
                .village("Test Village 2")
                .mobileNumber("9876543211")
                .centreId("C002")
                .centreName("Test Centre 2")
                .centreType("Secondary")
                .partnerName("Test Partner 2")
                .programName("Test Program 2")
                .landSizeUnderCultivation(3.0)
                .organicStatus("In Transition")
                .householdAnnualIncome(new BigDecimal("150000"))
                .yearData(Arrays.asList(yearData1))
                .build();

        testData = Arrays.asList(farmer1, farmer2);
    }

    @Test
    void testGenerateCottonFarmingReportExcel_Success() throws IOException {
        // When
        byte[] excelData = excelExportService.generateCottonFarmingReportExcel(testData);

        // Then
        assertNotNull(excelData);
        assertTrue(excelData.length > 0);
        
        // Verify it's a valid Excel file by checking the first few bytes (Excel magic number)
        // Excel files start with PK (ZIP format)
        assertEquals(0x50, excelData[0] & 0xFF); // 'P'
        assertEquals(0x4B, excelData[1] & 0xFF); // 'K'
    }

    @Test
    void testGenerateCottonFarmingReportExcel_EmptyData() throws IOException {
        // Given
        List<CottonFarmingReportDto> emptyData = new ArrayList<>();

        // When
        byte[] excelData = excelExportService.generateCottonFarmingReportExcel(emptyData);

        // Then
        assertNotNull(excelData);
        assertTrue(excelData.length > 0);
        
        // Even with empty data, should generate a valid Excel file with headers
        assertEquals(0x50, excelData[0] & 0xFF); // 'P'
        assertEquals(0x4B, excelData[1] & 0xFF); // 'K'
    }

    @Test
    void testGenerateFileName() {
        // Given
        List<Integer> years = Arrays.asList(2023, 2024);
        Long programId = 1L;
        Long partnerId = 2L;

        // When
        String filename = excelExportService.generateFileName("CottonFarming", programId, partnerId, years);

        // Then
        assertNotNull(filename);
        assertTrue(filename.contains("CottonFarming_Report"));
        assertTrue(filename.contains("Program1"));
        assertTrue(filename.contains("Partner2"));
        assertTrue(filename.contains("Years_2023_2024"));
        assertTrue(filename.endsWith(".xlsx"));
    }

    @Test
    void testGenerateFileName_NullParameters() {
        // When
        String filename = excelExportService.generateFileName("CottonFarming", null, null, null);

        // Then
        assertNotNull(filename);
        assertTrue(filename.contains("CottonFarming_Report"));
        assertTrue(filename.endsWith(".xlsx"));
        assertFalse(filename.contains("Program"));
        assertFalse(filename.contains("Partner"));
        assertFalse(filename.contains("Years"));
    }

    @Test
    void testGenerateFileName_EmptyYears() {
        // Given
        List<Integer> emptyYears = new ArrayList<>();

        // When
        String filename = excelExportService.generateFileName("CottonFarming", 1L, 2L, emptyYears);

        // Then
        assertNotNull(filename);
        assertTrue(filename.contains("CottonFarming_Report"));
        assertTrue(filename.contains("Program1"));
        assertTrue(filename.contains("Partner2"));
        assertFalse(filename.contains("Years"));
        assertTrue(filename.endsWith(".xlsx"));
    }
}
