#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa4efd5da8, pid=17200, tid=16020
#
# JRE version: Java(TM) SE Runtime Environment (21.0.4+8) (build 21.0.4+8-LTS-274)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21.0.4+8-LTS-274, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x355da8]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://bugreport.java.com/bugreport/crash.jsp
#

---------------  S U M M A R Y ------------

Command Line: -Dclassworlds.conf=C:\apache-maven-3.9.9\bin\m2.conf -Dmaven.home=C:\apache-maven-3.9.9 -Dlibrary.jansi.path=C:\apache-maven-3.9.9\lib\jansi-native -Dmaven.multiModuleProjectDirectory=D:\TECHNOSERVE\technoserve\Backend org.codehaus.plexus.classworlds.launcher.Launcher spring-boot:run -Dspring-boot.run.jvmArguments=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005

Host: Intel(R) Core(TM) i5-6300U CPU @ 2.40GHz, 4 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Mon May 26 14:26:45 2025 India Standard Time elapsed time: 7.744938 seconds (0d 0h 0m 7s)

---------------  T H R E A D  ---------------

Current thread (0x00000171e8221a70):  WorkerThread "GC Thread#0"    [id=16020, stack(0x0000002ebaf00000,0x0000002ebb000000) (1024K)]

Stack: [0x0000002ebaf00000,0x0000002ebb000000],  sp=0x0000002ebafff640,  free space=1021k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x355da8]
V  [jvm.dll+0x35249e]
V  [jvm.dll+0x35f11b]
V  [jvm.dll+0x360dd9]
V  [jvm.dll+0x35f5fa]
V  [jvm.dll+0x360a0f]
V  [jvm.dll+0x36bc23]
V  [jvm.dll+0x36bd6b]
V  [jvm.dll+0x880f17]
V  [jvm.dll+0x7fd65b]
V  [jvm.dll+0x6c753d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000000000010000


Registers:
RAX=0x0000000000000000, RBX=0x0000000708825004, RCX=0x0000000000000015, RDX=0x0000000000000000
RSP=0x0000002ebafff640, RBP=0x0000000000000c83, RSI=0x00000171e81d3bb0, RDI=0x00000171cd3ebc40
R8 =0x0000000000010000, R9 =0x0000000708823df0, R10=0x00007ffae4800000, R11=0x00007ffae48015e3
R12=0x0000000000000002, R13=0x00000171e6121f70, R14=0x0000000000000040, R15=0x0000000000000000
RIP=0x00007ffa4efd5da8, EFLAGS=0x0000000000010246


Register to memory mapping:

RAX=0x0 is null
RBX=
[error occurred during error reporting (printing register info), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa4edbd2f1]
RCX=0x0000000000000015 is an unknown value
RDX=0x0 is null
RSP=0x0000002ebafff640 points into unknown readable memory: 0x000000070bcd0000 | 00 00 cd 0b 07 00 00 00
RBP=0x0000000000000c83 is an unknown value
RSI=0x00000171e81d3bb0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
RDI=0x00000171cd3ebc40 points into unknown readable memory: 0x00007ffa4f653478 | 78 34 65 4f fa 7f 00 00
R8 =0x0000000000010000 is an unknown value
R9 =0x0000000708823df0 is an oop: org.apache.maven.model.InputSource 
{0x0000000708823df0} - klass: 'org/apache/maven/model/InputSource'
 - ---- fields (total size 3 words):
 - private 'modelId' 'Ljava/lang/String;' @12  "org.seleniumhq.selenium:selenium-bom:4.25.0"{0x0000000708823e08} (0xe11047c1)
 - private 'location' 'Ljava/lang/String;' @16  "C:\Users\<USER>\.m2\repository\org\seleniumhq\selenium\selenium-bom\4.25.0\selenium-bom-4.25.0.pom"{0x0000000708823e60} (0xe11047cc)
R10=0x00007ffae4800000 VCRUNTIME140.dll
R11=0x00007ffae48015e3 VCRUNTIME140.dll
R12=0x0000000000000002 is an unknown value
R13=0x00000171e6121f70 points into unknown readable memory: 0x0000000000000002 | 02 00 00 00 00 00 00 00
R14=0x0000000000000040 is an unknown value
R15=0x0 is null

Top of Stack: (sp=0x0000002ebafff640)
0x0000002ebafff640:   000000070bcd0000 0000000000000001
0x0000002ebafff650:   0000017100000000 0000000000000020
0x0000002ebafff660:   000000070262dc00 0000000000000001
0x0000002ebafff670:   0000002ebafff860 000000070262e060
0x0000002ebafff680:   0000002ebafff860 00007ffa4efd249e
0x0000002ebafff690:   0000002ebafff710 00000171cd3ebc40
0x0000002ebafff6a0:   000000070262ae00 0000000000000040
0x0000002ebafff6b0:   000000000499086a 0000000479a8e747
0x0000002ebafff6c0:   0000000000000002 0000002ebafff860
0x0000002ebafff6d0:   00000171e82476b0 00007ffa4efdf11b
0x0000002ebafff6e0:   0000000000000001 00000171e82476b0
0x0000002ebafff6f0:   0000002ebafff860 00007ffa4f341b1f
0x0000002ebafff700:   000000070262dc00 0000000000000080
0x0000002ebafff710:   00007ffa4f6546a0 0000000000000000
0x0000002ebafff720:   00000171e81cbfd0 00000171cd3ebc40
0x0000002ebafff730:   0000002ebafff8b0 00007ffa4f341b1f
0x0000002ebafff740:   00000171e6122da0 0000017180001180
0x0000002ebafff750:   00000171e81f9f70 00007ffa4efe0dd9
0x0000002ebafff760:   0000002ebafff7e0 00007ffa4f4883ce
0x0000002ebafff770:   0000000000000000 0000017180001170
0x0000002ebafff780:   0000000000000000 0000000000000000
0x0000002ebafff790:   00000171e6121f30 0000000000000010
0x0000002ebafff7a0:   00000171e81f9f70 0000002ebafff8f9
0x0000002ebafff7b0:   0000002ebafff860 00007ffa4efdf5fa
0x0000002ebafff7c0:   00000000000003d8 00000171e6122db0
0x0000002ebafff7d0:   00000171e6123188 00000171e82476b0
0x0000002ebafff7e0:   00000171cd3ebc40 000000000499072b
0x0000002ebafff7f0:   0000000479a7afd7 0000002ebafff8b8
0x0000002ebafff800:   0000002ebafff8c8 0000000000000000
0x0000002ebafff810:   00000171e81cbfd0 00007ffa4efe0a0f
0x0000002ebafff820:   0000000000000000 0000000000000008
0x0000002ebafff830:   00007ffa4f650760 000000000499071c 

Instructions: (pc=0x00007ffa4efd5da8)
0x00007ffa4efd5ca8:   ff 48 8d 53 fe 48 8b cf e8 8b f3 ff ff e9 d6 fd
0x00007ffa4efd5cb8:   ff ff 66 0f 1f 44 00 00 48 8b 77 10 8b ae 80 00
0x00007ffa4efd5cc8:   00 00 8b cd 8b 86 00 01 00 00 2b c8 81 e1 ff ff
0x00007ffa4efd5cd8:   01 00 41 3b ce 0f 86 48 02 00 00 ff cd 81 e5 ff
0x00007ffa4efd5ce8:   ff 01 00 89 ae 80 00 00 00 e8 3a 40 36 00 90 8b
0x00007ffa4efd5cf8:   d5 8b 86 00 01 00 00 8b cd 48 8b 9e 80 01 00 00
0x00007ffa4efd5d08:   2b c8 81 e1 ff ff 01 00 81 f9 ff ff 01 00 48 8b
0x00007ffa4efd5d18:   1c eb 41 0f 44 cf 85 c9 75 3d 90 89 6c 24 50 48
0x00007ffa4efd5d28:   8b 86 00 01 00 00 48 8b c8 48 c1 e9 20 ff c1 89
0x00007ffa4efd5d38:   4c 24 54 3b e8 0f 85 d6 01 00 00 48 c1 e1 20 48
0x00007ffa4efd5d48:   0b ca f0 48 0f b1 8e 00 01 00 00 0f 85 c0 01 00
0x00007ffa4efd5d58:   00 8b 86 00 01 00 00 f6 c3 01 0f 84 de 00 00 00
0x00007ffa4efd5d68:   8b 0d fa 11 86 00 48 ff cb 48 8b 47 08 44 8b 03
0x00007ffa4efd5d78:   49 d3 e0 8b 88 b0 05 00 00 48 8b 80 a0 05 00 00
0x00007ffa4efd5d88:   4c 03 05 d1 11 86 00 49 8b d0 48 d3 ea 0f b7 14
0x00007ffa4efd5d98:   50 0f b7 c2 66 c1 e8 08 84 c0 0f 88 18 ff ff ff
0x00007ffa4efd5da8:   4d 8b 08 41 0f b6 c1 24 03 3c 03 75 06 49 83 e1
0x00007ffa4efd5db8:   fc eb 0b 48 8b cf e8 1d ed ff ff 4c 8b c8 8b 0d
0x00007ffa4efd5dc8:   9c 11 86 00 49 8b d1 48 2b 15 8a 11 86 00 48 d3
0x00007ffa4efd5dd8:   ea 89 13 48 8b d3 8b 0d 5c a1 8c 00 49 33 d1 48
0x00007ffa4efd5de8:   d3 ea 48 85 d2 0f 84 cd fe ff ff 48 8b 47 08 4c
0x00007ffa4efd5df8:   8b 80 a0 05 00 00 8b 88 b0 05 00 00 48 8b c3 48
0x00007ffa4efd5e08:   d3 e8 41 0f b7 14 40 66 c1 ea 08 80 fa fe 0f 84
0x00007ffa4efd5e18:   a4 fe ff ff 49 8b c1 48 d3 e8 41 0f b7 14 40 0f
0x00007ffa4efd5e28:   b7 c2 66 c1 e8 08 84 c0 0f 89 8a fe ff ff 4c 8b
0x00007ffa4efd5e38:   c3 48 8b cf e8 ef d0 ff ff e9 7a fe ff ff f6 c3
0x00007ffa4efd5e48:   03 0f 85 b9 00 00 00 48 8b 47 08 4c 8b 03 49 8b
0x00007ffa4efd5e58:   d0 8b 88 b0 05 00 00 48 8b 80 a0 05 00 00 48 d3
0x00007ffa4efd5e68:   ea 0f b7 14 50 0f b7 c2 66 c1 e8 08 84 c0 0f 88
0x00007ffa4efd5e78:   44 fe ff ff 4d 8b 08 41 0f b6 c1 24 03 3c 03 75
0x00007ffa4efd5e88:   06 49 83 e1 fc eb 0b 48 8b cf e8 49 ec ff ff 4c
0x00007ffa4efd5e98:   8b c8 49 8b d1 4c 89 0b 8b 0d 9a a0 8c 00 48 33 


Stack slot to memory mapping:

stack at sp + 0 slots: 
[error occurred during error reporting (inspecting top of stack), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa4edbd2f1]
stack at sp + 1 slots: 0x0000000000000001 is an unknown value
stack at sp + 2 slots: 0x0000017100000000 is an unknown value
stack at sp + 3 slots: 0x0000000000000020 is an unknown value
stack at sp + 4 slots: 0x000000070262dc00 is an oop: java.util.concurrent.ConcurrentHashMap$Node 
{0x000000070262dc00} - klass: 'java/util/concurrent/ConcurrentHashMap$Node'
 - ---- fields (total size 4 words):
 - final 'hash' 'I' @12  721205713 (0x2afcb9d1)
 - final 'key' 'Ljava/lang/Object;' @16  "java.runtime.name"{0x00000007026891a0} (0xe04d1234)
 - volatile 'val' 'Ljava/lang/Object;' @20  "Java(TM) SE Runtime Environment"{0x000000070262dc20} (0xe04c5b84)
 - volatile 'next' 'Ljava/util/concurrent/ConcurrentHashMap$Node;' @24  null (0x00000000)
stack at sp + 5 slots: 0x0000000000000001 is an unknown value
stack at sp + 6 slots: 0x0000002ebafff860 points into unknown readable memory: 0x00007ffa4f654838 | 38 48 65 4f fa 7f 00 00
stack at sp + 7 slots: 0x000000070262e060 is an oop: java.lang.String 
{0x000000070262e060} - klass: 'java/lang/String'
 - string: "org.codehaus.plexus.classworlds.launcher.Launcher"
 - ---- fields (total size 3 words):
 - private 'hash' 'I' @12  0 (0x00000000)
 - private final 'coder' 'B' @16  0 (0x00)
 - private 'hashIsZero' 'Z' @17  false (0x00)
 - injected 'flags' 'B' @18  0 (0x00)
 - private final 'value' '[B' @20  [B{0x0000000702631f80} (0xe04c63f0)


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000171cd1e6b20, length=13, elements={
0x00000171e611f390, 0x0000017186b16ad0, 0x0000017186b17ba0, 0x0000017186b1fa40,
0x0000017186b20720, 0x0000017186b214e0, 0x0000017186b220f0, 0x0000017186b2f850,
0x0000017186b5c4e0, 0x00000171cc11ed90, 0x00000171cc1215d0, 0x00000171cd1911e0,
0x00000171cce62f60
}

Java Threads: ( => current thread )
  0x00000171e611f390 JavaThread "main"                              [_thread_blocked, id=13344, stack(0x0000002ebae00000,0x0000002ebaf00000) (1024K)]
  0x0000017186b16ad0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7360, stack(0x0000002ebb600000,0x0000002ebb700000) (1024K)]
  0x0000017186b17ba0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=5596, stack(0x0000002ebb700000,0x0000002ebb800000) (1024K)]
  0x0000017186b1fa40 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=2624, stack(0x0000002ebb800000,0x0000002ebb900000) (1024K)]
  0x0000017186b20720 JavaThread "Attach Listener"            daemon [_thread_blocked, id=8088, stack(0x0000002ebb900000,0x0000002ebba00000) (1024K)]
  0x0000017186b214e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=16788, stack(0x0000002ebba00000,0x0000002ebbb00000) (1024K)]
  0x0000017186b220f0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=7272, stack(0x0000002ebbb00000,0x0000002ebbc00000) (1024K)]
  0x0000017186b2f850 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=13812, stack(0x0000002ebbc00000,0x0000002ebbd00000) (1024K)]
  0x0000017186b5c4e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=15384, stack(0x0000002ebbd00000,0x0000002ebbe00000) (1024K)]
  0x00000171cc11ed90 JavaThread "Notification Thread"        daemon [_thread_blocked, id=11792, stack(0x0000002ebbe00000,0x0000002ebbf00000) (1024K)]
  0x00000171cc1215d0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=3860, stack(0x0000002ebbf00000,0x0000002ebc000000) (1024K)]
  0x00000171cd1911e0 JavaThread "Thread-1"                   daemon [_thread_blocked, id=16940, stack(0x0000002ebc400000,0x0000002ebc500000) (1024K)]
  0x00000171cce62f60 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=16076, stack(0x0000002ebc300000,0x0000002ebc400000) (1024K)]
Total: 13

Other Threads:
  0x0000017186af5280 VMThread "VM Thread"                           [id=6032, stack(0x0000002ebb500000,0x0000002ebb600000) (1024K)]
  0x0000017186adf180 WatcherThread "VM Periodic Task Thread"        [id=16892, stack(0x0000002ebb400000,0x0000002ebb500000) (1024K)]
=>0x00000171e8221a70 WorkerThread "GC Thread#0"                     [id=16020, stack(0x0000002ebaf00000,0x0000002ebb000000) (1024K)]
  0x00000171ccab4cc0 WorkerThread "GC Thread#1"                     [id=9424, stack(0x0000002ebc000000,0x0000002ebc100000) (1024K)]
  0x00000171ccab5060 WorkerThread "GC Thread#2"                     [id=15800, stack(0x0000002ebc100000,0x0000002ebc200000) (1024K)]
  0x00000171ccab5400 WorkerThread "GC Thread#3"                     [id=13424, stack(0x0000002ebc200000,0x0000002ebc300000) (1024K)]
  0x00000171e82329c0 ConcurrentGCThread "G1 Main Marker"            [id=9832, stack(0x0000002ebb000000,0x0000002ebb100000) (1024K)]
  0x00000171e8233c60 WorkerThread "G1 Conc#0"                       [id=10804, stack(0x0000002ebb100000,0x0000002ebb200000) (1024K)]
  0x00000171869ac110 ConcurrentGCThread "G1 Refine#0"               [id=16732, stack(0x0000002ebb200000,0x0000002ebb300000) (1024K)]
  0x00000171869ac930 ConcurrentGCThread "G1 Service"                [id=6744, stack(0x0000002ebb300000,0x0000002ebb400000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0     7835 4692       4       org.apache.maven.model.inheritance.DefaultInheritanceAssembler$InheritanceModelMerger::mergePluginContainer_Plugins (433 bytes)
C2 CompilerThread1     7835 4718       4       java.util.regex.Pattern$GroupHead::match (47 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffa4f8b7e98] Threads_lock - owner thread: 0x0000017186af5280
[0x00007ffa4f8b7f98] Heap_lock - owner thread: 0x00000171e611f390

Heap address: 0x0000000702400000, size: 4060 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000017187000000-0x0000017187c90000-0x0000017187c90000), size 13172736, SharedBaseAddress: 0x0000017187000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000017188000000-0x00000171c8000000, reserved size: 1073741824
Narrow klass base: 0x0000017187000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 4 total, 4 available
 Memory: 16239M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4060M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 157439K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 80 young (163840K), 6 survivors (12288K)
 Metaspace       used 16807K, committed 17088K, reserved 1114112K
  class space    used 1887K, committed 1984K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702400000| PB 0x0000000702400000| Untracked 
|   1|0x0000000702600000, 0x000000070272eef8, 0x0000000702800000| 59%| O|  |TAMS 0x0000000702600000| PB 0x0000000702600000| Untracked 
|   2|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|   3|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|   4|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|   5|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|   6|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|   7|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|   8|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|   9|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|  10|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|  11|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  12|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  13|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  14|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  15|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  16|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  17|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  18|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  19|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  20|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  21|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  22|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  23|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  24|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  25|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  26|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  27|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  28|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  29|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  30|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  31|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  32|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  33|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  34|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  35|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  36|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  37|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  38|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  39|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  40|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  41|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  42|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  43|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  44|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  45|0x0000000707e00000, 0x0000000707f82080, 0x0000000708000000| 75%| S|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Complete 
|  46|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| S|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Complete 
|  47|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| S|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Complete 
|  48|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| S|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Complete 
|  49|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| S|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Complete 
|  50|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| S|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Complete 
|  51|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| E|CS|TAMS 0x0000000708a00000| PB 0x0000000708a00000| Complete 
|  52|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| E|CS|TAMS 0x0000000708c00000| PB 0x0000000708c00000| Complete 
|  53|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| E|CS|TAMS 0x0000000708e00000| PB 0x0000000708e00000| Complete 
|  54|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| E|CS|TAMS 0x0000000709000000| PB 0x0000000709000000| Complete 
|  55|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| E|CS|TAMS 0x0000000709200000| PB 0x0000000709200000| Complete 
|  56|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| E|CS|TAMS 0x0000000709400000| PB 0x0000000709400000| Complete 
|  57|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| E|CS|TAMS 0x0000000709600000| PB 0x0000000709600000| Complete 
|  58|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| E|CS|TAMS 0x0000000709800000| PB 0x0000000709800000| Complete 
|  59|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| E|CS|TAMS 0x0000000709a00000| PB 0x0000000709a00000| Complete 
|  60|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| E|CS|TAMS 0x0000000709c00000| PB 0x0000000709c00000| Complete 
|  61|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| E|CS|TAMS 0x0000000709e00000| PB 0x0000000709e00000| Complete 
|  62|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| E|CS|TAMS 0x000000070a000000| PB 0x000000070a000000| Complete 
|  63|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| E|CS|TAMS 0x000000070a200000| PB 0x000000070a200000| Complete 
|  64|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| E|CS|TAMS 0x000000070a400000| PB 0x000000070a400000| Complete 
|  65|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| E|CS|TAMS 0x000000070a600000| PB 0x000000070a600000| Complete 
|  66|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| E|CS|TAMS 0x000000070a800000| PB 0x000000070a800000| Complete 
|  67|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| E|CS|TAMS 0x000000070aa00000| PB 0x000000070aa00000| Complete 
|  68|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| E|CS|TAMS 0x000000070ac00000| PB 0x000000070ac00000| Complete 
|  69|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| E|CS|TAMS 0x000000070ae00000| PB 0x000000070ae00000| Complete 
|  70|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| E|CS|TAMS 0x000000070b000000| PB 0x000000070b000000| Complete 
|  71|0x000000070b200000, 0x000000070b400000, 0x000000070b400000|100%| E|CS|TAMS 0x000000070b200000| PB 0x000000070b200000| Complete 
|  72|0x000000070b400000, 0x000000070b600000, 0x000000070b600000|100%| E|CS|TAMS 0x000000070b400000| PB 0x000000070b400000| Complete 
|  73|0x000000070b600000, 0x000000070b800000, 0x000000070b800000|100%| E|CS|TAMS 0x000000070b600000| PB 0x000000070b600000| Complete 
|  74|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| E|CS|TAMS 0x000000070b800000| PB 0x000000070b800000| Complete 
|  75|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| E|CS|TAMS 0x000000070ba00000| PB 0x000000070ba00000| Complete 
|  76|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| E|CS|TAMS 0x000000070bc00000| PB 0x000000070bc00000| Complete 
|  77|0x000000070be00000, 0x000000070c000000, 0x000000070c000000|100%| E|CS|TAMS 0x000000070be00000| PB 0x000000070be00000| Complete 
|  78|0x000000070c000000, 0x000000070c200000, 0x000000070c200000|100%| E|CS|TAMS 0x000000070c000000| PB 0x000000070c000000| Complete 
|  79|0x000000070c200000, 0x000000070c400000, 0x000000070c400000|100%| E|CS|TAMS 0x000000070c200000| PB 0x000000070c200000| Complete 
|  80|0x000000070c400000, 0x000000070c600000, 0x000000070c600000|100%| E|CS|TAMS 0x000000070c400000| PB 0x000000070c400000| Complete 
|  81|0x000000070c600000, 0x000000070c800000, 0x000000070c800000|100%| E|CS|TAMS 0x000000070c600000| PB 0x000000070c600000| Complete 
|  82|0x000000070c800000, 0x000000070ca00000, 0x000000070ca00000|100%| E|CS|TAMS 0x000000070c800000| PB 0x000000070c800000| Complete 
|  83|0x000000070ca00000, 0x000000070cc00000, 0x000000070cc00000|100%| E|CS|TAMS 0x000000070ca00000| PB 0x000000070ca00000| Complete 
|  84|0x000000070cc00000, 0x000000070ce00000, 0x000000070ce00000|100%| E|CS|TAMS 0x000000070cc00000| PB 0x000000070cc00000| Complete 
|  85|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%| E|CS|TAMS 0x000000070ce00000| PB 0x000000070ce00000| Complete 
|  86|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%| E|CS|TAMS 0x000000070d000000| PB 0x000000070d000000| Complete 
|  87|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%| E|CS|TAMS 0x000000070d200000| PB 0x000000070d200000| Complete 
|  88|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%| E|CS|TAMS 0x000000070d400000| PB 0x000000070d400000| Complete 
|  89|0x000000070d600000, 0x000000070d800000, 0x000000070d800000|100%| E|CS|TAMS 0x000000070d600000| PB 0x000000070d600000| Complete 
|  90|0x000000070d800000, 0x000000070da00000, 0x000000070da00000|100%| E|CS|TAMS 0x000000070d800000| PB 0x000000070d800000| Complete 
|  91|0x000000070da00000, 0x000000070dc00000, 0x000000070dc00000|100%| E|CS|TAMS 0x000000070da00000| PB 0x000000070da00000| Complete 
|  92|0x000000070dc00000, 0x000000070de00000, 0x000000070de00000|100%| E|CS|TAMS 0x000000070dc00000| PB 0x000000070dc00000| Complete 
|  93|0x000000070de00000, 0x000000070e000000, 0x000000070e000000|100%| E|CS|TAMS 0x000000070de00000| PB 0x000000070de00000| Complete 
|  94|0x000000070e000000, 0x000000070e200000, 0x000000070e200000|100%| E|CS|TAMS 0x000000070e000000| PB 0x000000070e000000| Complete 
|  95|0x000000070e200000, 0x000000070e400000, 0x000000070e400000|100%| E|CS|TAMS 0x000000070e200000| PB 0x000000070e200000| Complete 
|  96|0x000000070e400000, 0x000000070e600000, 0x000000070e600000|100%| E|CS|TAMS 0x000000070e400000| PB 0x000000070e400000| Complete 
|  97|0x000000070e600000, 0x000000070e800000, 0x000000070e800000|100%| E|CS|TAMS 0x000000070e600000| PB 0x000000070e600000| Complete 
|  98|0x000000070e800000, 0x000000070ea00000, 0x000000070ea00000|100%| E|CS|TAMS 0x000000070e800000| PB 0x000000070e800000| Complete 
|  99|0x000000070ea00000, 0x000000070ec00000, 0x000000070ec00000|100%| E|CS|TAMS 0x000000070ea00000| PB 0x000000070ea00000| Complete 
| 100|0x000000070ec00000, 0x000000070ee00000, 0x000000070ee00000|100%| E|CS|TAMS 0x000000070ec00000| PB 0x000000070ec00000| Complete 
| 101|0x000000070ee00000, 0x000000070f000000, 0x000000070f000000|100%| E|CS|TAMS 0x000000070ee00000| PB 0x000000070ee00000| Complete 
| 102|0x000000070f000000, 0x000000070f200000, 0x000000070f200000|100%| E|CS|TAMS 0x000000070f000000| PB 0x000000070f000000| Complete 
| 103|0x000000070f200000, 0x000000070f400000, 0x000000070f400000|100%| E|CS|TAMS 0x000000070f200000| PB 0x000000070f200000| Complete 
| 104|0x000000070f400000, 0x000000070f600000, 0x000000070f600000|100%| E|CS|TAMS 0x000000070f400000| PB 0x000000070f400000| Complete 
| 105|0x000000070f600000, 0x000000070f800000, 0x000000070f800000|100%| E|CS|TAMS 0x000000070f600000| PB 0x000000070f600000| Complete 
| 106|0x000000070f800000, 0x000000070fa00000, 0x000000070fa00000|100%| E|CS|TAMS 0x000000070f800000| PB 0x000000070f800000| Complete 
| 107|0x000000070fa00000, 0x000000070fa910a0, 0x000000070fc00000| 28%| E|CS|TAMS 0x000000070fa00000| PB 0x000000070fa00000| Complete 
| 108|0x000000070fc00000, 0x000000070fe00000, 0x000000070fe00000|100%| E|CS|TAMS 0x000000070fc00000| PB 0x000000070fc00000| Complete 
| 109|0x000000070fe00000, 0x0000000710000000, 0x0000000710000000|100%| E|CS|TAMS 0x000000070fe00000| PB 0x000000070fe00000| Complete 
| 110|0x0000000710000000, 0x0000000710200000, 0x0000000710200000|100%| E|CS|TAMS 0x0000000710000000| PB 0x0000000710000000| Complete 
| 111|0x0000000710200000, 0x0000000710400000, 0x0000000710400000|100%| E|CS|TAMS 0x0000000710200000| PB 0x0000000710200000| Complete 
| 112|0x0000000710400000, 0x0000000710600000, 0x0000000710600000|100%| E|CS|TAMS 0x0000000710400000| PB 0x0000000710400000| Complete 
| 113|0x0000000710600000, 0x0000000710800000, 0x0000000710800000|100%| E|CS|TAMS 0x0000000710600000| PB 0x0000000710600000| Complete 
| 114|0x0000000710800000, 0x0000000710a00000, 0x0000000710a00000|100%| E|CS|TAMS 0x0000000710800000| PB 0x0000000710800000| Complete 
| 115|0x0000000710a00000, 0x0000000710c00000, 0x0000000710c00000|100%| E|CS|TAMS 0x0000000710a00000| PB 0x0000000710a00000| Complete 
| 116|0x0000000710c00000, 0x0000000710e00000, 0x0000000710e00000|100%| E|CS|TAMS 0x0000000710c00000| PB 0x0000000710c00000| Complete 
| 117|0x0000000710e00000, 0x0000000711000000, 0x0000000711000000|100%| E|CS|TAMS 0x0000000710e00000| PB 0x0000000710e00000| Complete 
| 118|0x0000000711000000, 0x0000000711200000, 0x0000000711200000|100%| E|CS|TAMS 0x0000000711000000| PB 0x0000000711000000| Complete 
| 119|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%| E|CS|TAMS 0x0000000711200000| PB 0x0000000711200000| Complete 
| 120|0x0000000711400000, 0x0000000711600000, 0x0000000711600000|100%| E|CS|TAMS 0x0000000711400000| PB 0x0000000711400000| Complete 
| 121|0x0000000711600000, 0x0000000711800000, 0x0000000711800000|100%| E|CS|TAMS 0x0000000711600000| PB 0x0000000711600000| Complete 
| 122|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| E|CS|TAMS 0x0000000711800000| PB 0x0000000711800000| Complete 
| 123|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| E|CS|TAMS 0x0000000711a00000| PB 0x0000000711a00000| Complete 
| 124|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| E|CS|TAMS 0x0000000711c00000| PB 0x0000000711c00000| Complete 
| 125|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| E|CS|TAMS 0x0000000711e00000| PB 0x0000000711e00000| Complete 
| 126|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete 

Card table byte_map: [0x0000017180000000,0x00000171807f0000] _byte_map_base: 0x000001717c7ee000

Marking Bits: (CMBitMap*) 0x00000171e8222170
 Bits: [0x00000171807f0000, 0x0000017184760000)

Polling page: 0x00000171e6060000

Metaspace:

Usage:
  Non-class:     14.57 MB used.
      Class:      1.84 MB used.
       Both:     16.41 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      14.75 MB ( 23%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      16.69 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  1.09 MB
       Class:  13.90 MB
        Both:  15.00 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 178.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 267.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 740.
num_chunk_merges: 0.
num_chunk_splits: 515.
num_chunks_enlarged: 427.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2333Kb max_used=2333Kb free=117666Kb
 bounds [0x00000171f8110000, 0x00000171f8380000, 0x00000171ff640000]
CodeHeap 'profiled nmethods': size=120000Kb used=10269Kb max_used=10269Kb free=109730Kb
 bounds [0x00000171f0640000, 0x00000171f1050000, 0x00000171f7b70000]
CodeHeap 'non-nmethods': size=5760Kb used=1399Kb max_used=1479Kb free=4360Kb
 bounds [0x00000171f7b70000, 0x00000171f7de0000, 0x00000171f8110000]
 total_blobs=5212 nmethods=4720 adapters=396
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 7.655 Thread 0x00000171cce62f60 nmethod 4695% 0x00000171f8351390 code [0x00000171f8351680, 0x00000171f8352af0]
Event: 7.655 Thread 0x00000171cce62f60 4711   !   4       java.util.regex.Pattern::closure (276 bytes)
Event: 7.662 Thread 0x0000017186b5c4e0 4717       3       java.util.LinkedHashMap$LinkedHashIterator::remove (75 bytes)
Event: 7.662 Thread 0x0000017186b5c4e0 nmethod 4717 0x00000171f103f910 code [0x00000171f103fb00, 0x00000171f103ff08]
Event: 7.662 Thread 0x00000171cce62f60 nmethod 4711 0x00000171f8353790 code [0x00000171f8353960, 0x00000171f8353ed8]
Event: 7.663 Thread 0x00000171cce62f60 4697       4       org.apache.maven.model.merge.ModelMerger$DependencyKeyComputer::<init> (6 bytes)
Event: 7.664 Thread 0x00000171cce62f60 nmethod 4697 0x00000171f8354110 code [0x00000171f8354280, 0x00000171f83543f8]
Event: 7.664 Thread 0x00000171cce62f60 4694       4       org.eclipse.aether.graph.Dependency::<init> (13 bytes)
Event: 7.691 Thread 0x00000171cce62f60 nmethod 4694 0x00000171f8354490 code [0x00000171f83546c0, 0x00000171f8355500]
Event: 7.691 Thread 0x00000171cce62f60 4696       4       org.apache.maven.model.inheritance.DefaultInheritanceAssembler$InheritanceModelMerger::mergePlugin (71 bytes)
Event: 7.699 Thread 0x0000017186b5c4e0 4719       3       org.apache.maven.model.io.xpp3.MavenXpp3Reader::parseProfile (722 bytes)
Event: 7.704 Thread 0x0000017186b5c4e0 nmethod 4719 0x00000171f1040110 code [0x00000171f10409c0, 0x00000171f1045510]
Event: 7.707 Thread 0x0000017186b5c4e0 4720       3       java.util.stream.IntStream::empty (8 bytes)
Event: 7.707 Thread 0x0000017186b5c4e0 nmethod 4720 0x00000171f1046b90 code [0x00000171f1046d40, 0x00000171f1046ec8]
Event: 7.707 Thread 0x0000017186b5c4e0 4721       3       java.util.Spliterators::emptyIntSpliterator (4 bytes)
Event: 7.707 Thread 0x0000017186b5c4e0 nmethod 4721 0x00000171f1046f90 code [0x00000171f1047120, 0x00000171f1047218]
Event: 7.712 Thread 0x00000171cce62f60 nmethod 4696 0x00000171f8355c90 code [0x00000171f8355f60, 0x00000171f8356d40]
Event: 7.712 Thread 0x00000171cce62f60 4718       4       java.util.regex.Pattern$GroupHead::match (47 bytes)
Event: 7.713 Thread 0x0000017186b5c4e0 4722       3       org.apache.maven.model.merge.ModelMerger::mergeDependencyManagement (10 bytes)
Event: 7.713 Thread 0x0000017186b5c4e0 nmethod 4722 0x00000171f1047290 code [0x00000171f1047440, 0x00000171f1047600]

GC Heap History (7 events):
Event: 1.049 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 260096K, used 12288K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 0 survivors (0K)
 Metaspace       used 3601K, committed 3776K, reserved 1114112K
  class space    used 368K, committed 448K, reserved 1048576K
}
Event: 1.055 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 3182K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 3601K, committed 3776K, reserved 1114112K
  class space    used 368K, committed 448K, reserved 1048576K
}
Event: 1.388 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 260096K, used 13422K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 1 survivors (2048K)
 Metaspace       used 5684K, committed 5888K, reserved 1114112K
  class space    used 665K, committed 768K, reserved 1048576K
}
Event: 1.393 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 3989K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 5684K, committed 5888K, reserved 1114112K
  class space    used 665K, committed 768K, reserved 1048576K
}
Event: 2.656 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 38805K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 18 young (36864K), 1 survivors (2048K)
 Metaspace       used 11140K, committed 11328K, reserved 1114112K
  class space    used 1327K, committed 1408K, reserved 1048576K
}
Event: 2.660 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 5887K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 11140K, committed 11328K, reserved 1114112K
  class space    used 1327K, committed 1408K, reserved 1048576K
}
Event: 7.713 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 157439K [0x0000000702400000, 0x0000000800000000)
  region size 2048K, 76 young (155648K), 2 survivors (4096K)
 Metaspace       used 16807K, committed 17088K, reserved 1114112K
  class space    used 1887K, committed 1984K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.011 Loaded shared library C:\Program Files\Java\jdk-21\bin\java.dll
Event: 0.051 Loaded shared library C:\Program Files\Java\jdk-21\bin\jsvml.dll
Event: 0.077 Loaded shared library C:\Program Files\Java\jdk-21\bin\net.dll
Event: 0.082 Loaded shared library C:\Program Files\Java\jdk-21\bin\nio.dll
Event: 0.088 Loaded shared library C:\Program Files\Java\jdk-21\bin\zip.dll
Event: 0.238 Loaded shared library C:\Program Files\Java\jdk-21\bin\jimage.dll
Event: 0.239 Loaded shared library C:\apache-maven-3.9.9\lib\jansi-native\Windows\x86_64\jansi.dll
Event: 0.261 Loaded shared library C:\Program Files\Java\jdk-21\bin\verify.dll

Deoptimization events (20 events):
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000171f83301ac relative=0x000000000000028c
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000171f83301ac method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 6.813 Thread 0x00000171e611f390 DEOPT PACKING pc=0x00000171f83301ac sp=0x0000002ebaefb060
Event: 6.813 Thread 0x00000171e611f390 DEOPT UNPACKING pc=0x00000171f7bc46a2 sp=0x0000002ebaefb000 mode 2
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000171f83301ac relative=0x000000000000028c
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000171f83301ac method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 6.813 Thread 0x00000171e611f390 DEOPT PACKING pc=0x00000171f83301ac sp=0x0000002ebaefafa0
Event: 6.813 Thread 0x00000171e611f390 DEOPT UNPACKING pc=0x00000171f7bc46a2 sp=0x0000002ebaefaf40 mode 2
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000171f83301ac relative=0x000000000000028c
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000171f83301ac method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 6.813 Thread 0x00000171e611f390 DEOPT PACKING pc=0x00000171f83301ac sp=0x0000002ebaefb410
Event: 6.813 Thread 0x00000171e611f390 DEOPT UNPACKING pc=0x00000171f7bc46a2 sp=0x0000002ebaefb3b0 mode 2
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000171f83301ac relative=0x000000000000028c
Event: 6.813 Thread 0x00000171e611f390 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000171f83301ac method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 6.813 Thread 0x00000171e611f390 DEOPT PACKING pc=0x00000171f83301ac sp=0x0000002ebaefb350
Event: 6.813 Thread 0x00000171e611f390 DEOPT UNPACKING pc=0x00000171f7bc46a2 sp=0x0000002ebaefb2f0 mode 2
Event: 7.325 Thread 0x00000171e611f390 Uncommon trap: trap_request=0xffffff76 fr.pc=0x00000171f8343408 relative=0x0000000000000268
Event: 7.325 Thread 0x00000171e611f390 Uncommon trap: reason=predicate action=maybe_recompile pc=0x00000171f8343408 method=java.util.HashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 48 c2
Event: 7.325 Thread 0x00000171e611f390 DEOPT PACKING pc=0x00000171f8343408 sp=0x0000002ebaefc1a0
Event: 7.325 Thread 0x00000171e611f390 DEOPT UNPACKING pc=0x00000171f7bc46a2 sp=0x0000002ebaefc138 mode 2

Classes loaded (20 events):
Event: 3.454 Loading class sun/util/resources/LocaleData$LocaleDataResourceBundleProvider done
Event: 3.454 Loading class sun/util/resources/LocaleData$CommonResourceBundleProvider done
Event: 3.455 Loading class sun/util/resources/Bundles$2
Event: 3.455 Loading class sun/util/resources/Bundles$2 done
Event: 3.506 Loading class java/util/StringTokenizer
Event: 3.507 Loading class java/util/StringTokenizer done
Event: 3.644 Loading class java/util/stream/Streams$RangeIntSpliterator
Event: 3.644 Loading class java/util/stream/Streams$RangeIntSpliterator done
Event: 4.109 Loading class java/util/stream/MatchOps$MatchKind
Event: 4.110 Loading class java/util/stream/MatchOps$MatchKind done
Event: 4.110 Loading class java/util/stream/MatchOps
Event: 4.110 Loading class java/util/stream/MatchOps done
Event: 4.110 Loading class java/util/stream/MatchOps$MatchOp
Event: 4.110 Loading class java/util/stream/MatchOps$MatchOp done
Event: 4.111 Loading class java/util/stream/MatchOps$BooleanTerminalSink
Event: 4.111 Loading class java/util/stream/MatchOps$BooleanTerminalSink done
Event: 4.111 Loading class java/util/stream/MatchOps$1MatchSink
Event: 4.111 Loading class java/util/stream/MatchOps$1MatchSink done
Event: 6.695 Loading class java/util/Collections$UnmodifiableList$1
Event: 6.695 Loading class java/util/Collections$UnmodifiableList$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.633 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ff766f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x000000070ff766f8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.635 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ff821e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070ff821e8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.636 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ff8a308}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070ff8a308) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.642 Thread 0x00000171e611f390 Implicit null exception at 0x00000171f818e8c0 to 0x00000171f818eac4
Event: 2.644 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x000000070ffae8d8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, int, java.lang.Object)'> (0x000000070ffae8d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.981 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711817088}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711817088) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.013 Thread 0x00000171e611f390 Exception <a 'java/lang/NoClassDefFoundError'{0x00000007118a5fb0}: com/google/inject/servlet/ServletModuleTargetVisitor> (0x00000007118a5fb0) 
thrown [s\open\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 3.103 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x000000071165d5b8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000071165d5b8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.153 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x00000007116dcd38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x00000007116dcd38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.329 Thread 0x00000171e611f390 Implicit null exception at 0x00000171f813ac24 to 0x00000171f813b2bc
Event: 3.329 Thread 0x00000171e611f390 Implicit null exception at 0x00000171f81494a0 to 0x00000171f8149b3c
Event: 3.396 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711094300}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711094300) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.401 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x00000007110aa420}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007110aa420) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.428 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x000000071119fcc8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000071119fcc8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.520 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710f6d9d8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, int)'> (0x0000000710f6d9d8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.520 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710f71778}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000710f71778) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.521 Thread 0x00000171e611f390 Exception <a 'java/lang/NoSuchMethodError'{0x0000000710f74e38}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, int)'> (0x0000000710f74e38) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.205 Thread 0x00000171e611f390 Implicit null exception at 0x00000171f8242ef0 to 0x00000171f824430c
Event: 4.205 Thread 0x00000171e611f390 Implicit null exception at 0x00000171f81c2020 to 0x00000171f81c222c
Event: 4.205 Thread 0x00000171e611f390 Implicit null exception at 0x00000171f8229e0d to 0x00000171f822a6e4

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3.099 Executing VM operation: ICBufferFull done
Event: 3.117 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.117 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.172 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.172 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.215 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.215 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.280 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.280 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.624 Executing VM operation: ICBufferFull
Event: 3.625 Executing VM operation: ICBufferFull done
Event: 4.149 Executing VM operation: ICBufferFull
Event: 4.149 Executing VM operation: ICBufferFull done
Event: 5.154 Executing VM operation: Cleanup
Event: 5.155 Executing VM operation: Cleanup done
Event: 5.950 Executing VM operation: ICBufferFull
Event: 5.950 Executing VM operation: ICBufferFull done
Event: 6.956 Executing VM operation: Cleanup
Event: 6.966 Executing VM operation: Cleanup done
Event: 7.713 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Events (20 events):
Event: 0.048 Thread 0x0000017186b16ad0 Thread added: 0x0000017186b16ad0
Event: 0.048 Thread 0x0000017186b17ba0 Thread added: 0x0000017186b17ba0
Event: 0.048 Thread 0x0000017186b1fa40 Thread added: 0x0000017186b1fa40
Event: 0.048 Thread 0x0000017186b20720 Thread added: 0x0000017186b20720
Event: 0.048 Thread 0x0000017186b214e0 Thread added: 0x0000017186b214e0
Event: 0.048 Thread 0x0000017186b220f0 Thread added: 0x0000017186b220f0
Event: 0.049 Thread 0x0000017186b2f850 Thread added: 0x0000017186b2f850
Event: 0.049 Thread 0x0000017186b5c4e0 Thread added: 0x0000017186b5c4e0
Event: 0.072 Thread 0x00000171cc11ed90 Thread added: 0x00000171cc11ed90
Event: 0.074 Thread 0x00000171cc1215d0 Thread added: 0x00000171cc1215d0
Event: 0.236 Thread 0x00000171cc36e460 Thread added: 0x00000171cc36e460
Event: 0.253 Thread 0x00000171cc36e460 Thread exited: 0x00000171cc36e460
Event: 1.300 Thread 0x00000171ccbee890 Thread added: 0x00000171ccbee890
Event: 2.176 Thread 0x00000171ccbee890 Thread exited: 0x00000171ccbee890
Event: 2.800 Thread 0x00000171cce5f8e0 Thread added: 0x00000171cce5f8e0
Event: 3.238 Thread 0x00000171cd1911e0 Thread added: 0x00000171cd1911e0
Event: 4.533 Thread 0x00000171cce5f8e0 Thread exited: 0x00000171cce5f8e0
Event: 4.782 Thread 0x00000171cce61af0 Thread added: 0x00000171cce61af0
Event: 6.321 Thread 0x00000171cce61af0 Thread exited: 0x00000171cce61af0
Event: 7.506 Thread 0x00000171cce62f60 Thread added: 0x00000171cce62f60


Dynamic libraries:
0x00007ff752530000 - 0x00007ff752540000 	C:\Program Files\Java\jdk-21\bin\java.exe
0x00007ffaedaf0000 - 0x00007ffaedce8000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffaed880000 - 0x00007ffaed942000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffaeb820000 - 0x00007ffaebb16000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffaeb300000 - 0x00007ffaeb400000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffae8f40000 - 0x00007ffae8f59000 	C:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ffae4800000 - 0x00007ffae481b000 	C:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ffaec2c0000 - 0x00007ffaec36f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffaed0d0000 - 0x00007ffaed16e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffaed950000 - 0x00007ffaed9ef000 	C:\WINDOWS\System32\sechost.dll
0x00007ffaebb20000 - 0x00007ffaebc43000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffaeb6b0000 - 0x00007ffaeb6d7000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffaecf30000 - 0x00007ffaed0cd000 	C:\WINDOWS\System32\USER32.dll
0x00007ffaea530000 - 0x00007ffaea7ca000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffaeb680000 - 0x00007ffaeb6a2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffaebc50000 - 0x00007ffaebc7b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffaeb560000 - 0x00007ffaeb67a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffaeb210000 - 0x00007ffaeb2ad000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffae2870000 - 0x00007ffae287a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffaebca0000 - 0x00007ffaebccf000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffae8fe0000 - 0x00007ffae8fec000 	C:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ffac3e90000 - 0x00007ffac3f1e000 	C:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ffa4ec80000 - 0x00007ffa4f993000 	C:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ffaed320000 - 0x00007ffaed38b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffaeaff0000 - 0x00007ffaeb03b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffad61e0000 - 0x00007ffad6207000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffaeafd0000 - 0x00007ffaeafe2000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffae92d0000 - 0x00007ffae92e2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffae8f30000 - 0x00007ffae8f3a000 	C:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ffaeaad0000 - 0x00007ffaeacd1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffaeaa90000 - 0x00007ffaeaac4000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffaeb6e0000 - 0x00007ffaeb762000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffae47e0000 - 0x00007ffae47ff000 	C:\Program Files\Java\jdk-21\bin\java.dll
0x00007ffaec630000 - 0x00007ffaecd9e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffae8030000 - 0x00007ffae87d3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffaebf60000 - 0x00007ffaec2b3000 	C:\WINDOWS\System32\combase.dll
0x00007ffaea3b0000 - 0x00007ffaea3db000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffaec430000 - 0x00007ffaec4fd000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffaed9f0000 - 0x00007ffaeda9d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffaec370000 - 0x00007ffaec3c5000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffaeb0c0000 - 0x00007ffaeb0e5000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffab8710000 - 0x00007ffab87e7000 	C:\Program Files\Java\jdk-21\bin\jsvml.dll
0x00007ffae8d70000 - 0x00007ffae8d80000 	C:\Program Files\Java\jdk-21\bin\net.dll
0x00007ffadfff0000 - 0x00007ffae00fa000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffaea190000 - 0x00007ffaea1fc000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffae4760000 - 0x00007ffae4776000 	C:\Program Files\Java\jdk-21\bin\nio.dll
0x00007ffae4740000 - 0x00007ffae4758000 	C:\Program Files\Java\jdk-21\bin\zip.dll
0x00007ffade680000 - 0x00007ffade6a4000 	C:\apache-maven-3.9.9\lib\jansi-native\Windows\x86_64\jansi.dll
0x00007ffae3fb0000 - 0x00007ffae3fc0000 	C:\Program Files\Java\jdk-21\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;C:\Program Files\Java\jdk-21\bin\server;C:\apache-maven-3.9.9\lib\jansi-native\Windows\x86_64

VM Arguments:
jvm_args: -Dclassworlds.conf=C:\apache-maven-3.9.9\bin\m2.conf -Dmaven.home=C:\apache-maven-3.9.9 -Dlibrary.jansi.path=C:\apache-maven-3.9.9\lib\jansi-native -Dmaven.multiModuleProjectDirectory=D:\TECHNOSERVE\technoserve\Backend 
java_command: org.codehaus.plexus.classworlds.launcher.Launcher spring-boot:run -Dspring-boot.run.jvmArguments=-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
java_class_path (initial): C:\apache-maven-3.9.9\boot\plexus-classworlds-2.8.0.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4257218560                                {product} {ergonomic}
   size_t MaxNewSize                               = 2554331136                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4257218560                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-21
PATH=C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;C:\Program Files\nodejs\;C:\nginx\;;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\PuTTY\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Java\jdk-21\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2024.2.1\bin;;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.2.2\bin;;C:\Program Files\Git\bin;C:\apache-maven-3.9.9\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\PostgreSQL\16\bin;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;C:\Users\<USER>\AppData\Local\Google\Cloud SDK\google-cloud-sdk\bin;;C:\Users\<USER>\AppData\Local\Programs\Ollama
USERNAME=PS-L-120
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 78 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 3:35 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 78 stepping 3 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 4 processors :
  Max Mhz: 2501, Current Mhz: 2501, Mhz Limit: 2501

Memory: 4k page, system-wide physical 16239M (6241M free)
TotalPageFile size 26479M (AvailPageFile size 11403M)
current process WorkingSet (physical memory assigned to process): 266M, peak: 266M
current process commit charge ("private bytes"): 390M, peak: 392M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21.0.4+8-LTS-274) for windows-amd64 JRE (21.0.4+8-LTS-274), built on 2024-06-05T05:23:33Z by "mach5one" with MS VC++ 17.6 (VS2022)

END.
